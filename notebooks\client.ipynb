import sys
sys.path.append("..")

import dotenv
dotenv.load_dotenv()

import os
ACCOUNT = str(os.getenv("ACCOUNT"))
PASSWORD = str(os.getenv("PASSWORD"))


from src.taifex import Client

import logging


client = Client(ACCOUNT, PASSWORD, log_level=logging.DEBUG)

await client.init_session()

await client.login()

games = await client.get_game_list()

games


await client.enter_game(games[0].game_id)

async def example_handler(data):
    print("===========")
    print("EXAMPLE_HANDLER:")
    print(data)
    print("===========")

client.register_message_callback(example_handler)

await client.connect_websocket()

await client.subscribe(["0000.TW"])


await client.subscribe(["TXFF5.TW-A"])

async def test2_handler(data):
    print("===========")
    print("TEST2_HANDLER:")
    print(data)
    print("===========")
client.register_message_callback(test2_handler)

