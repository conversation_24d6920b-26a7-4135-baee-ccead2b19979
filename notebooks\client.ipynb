import sys
sys.path.append("..")

import dotenv
dotenv.load_dotenv()

import os
ACCOUNT = str(os.getenv("ACCOUNT"))
PASSWORD = str(os.getenv("PASSWORD"))


from src.taifex import Client

import logging


client = Client(ACCOUNT, PASSWORD, log_level=logging.DEBUG)

await client.init_session()

await client.login()

games = await client.get_game_list()

games


await client.enter_game(games[0].game_id)

async def example_handler(data):
    print("===========")
    print("EXAMPLE_HANDLER:")
    print(data)
    print("===========")

client.register_message_callback(example_handler)

await client.connect_websocket()

await client.subscribe(["0000.TW"])


await client.subscribe(["TXFF5.TW-A"])

async def test2_handler(data):
    print("===========")
    print("TEST2_HANDLER:")
    print(data)
    print("===========")
client.register_message_callback(test2_handler)

# 1. 檢查目前的遊戲資訊
print("目前選擇的遊戲:")
print(f"遊戲ID: {games[0].game_id}")
print(f"遊戲名稱: {games[0].game_name}")
print(f"市場模式: {games[0].market_mode}")
print(f"市場名稱: {games[0].market_name}")
print(f"時間範圍: {games[0].date_duration}")

# 4. 嘗試訂閱更常見的期貨商品
# 台指期貨的常見代碼
common_products = [
    "TXF",      # 台指期貨
    "TXFF5",    # 台指期貨 (特定月份)
    "MXF",      # 小台指期貨
    "MXFF5",    # 小台指期貨 (特定月份)
]

print("嘗試訂閱常見期貨商品:")
for product in common_products:
    try:
        success = await client.subscribe([product])
        print(f"訂閱 {product}: {'成功' if success else '失敗'}")
    except Exception as e:
        print(f"訂閱 {product} 時發生錯誤: {e}")

# 5. 創建一個更詳細的回調函數來檢查收到的數據
import datetime

async def detailed_handler(data):
    timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"\n[{timestamp}] 收到訊息:")
    print(f"類型: {data.get('type', 'unknown')}")

    if data.get('type') == 'data':
        payload = data.get('payload', {})
        print(f"數據內容: {payload}")

        # 檢查是否包含價格相關的欄位
        price_fields = ['price', 'bid', 'ask', 'last', 'close', 'open', 'high', 'low']
        found_price_fields = [field for field in price_fields if field in str(payload).lower()]
        if found_price_fields:
            print(f"發現價格相關欄位: {found_price_fields}")
    else:
        print(f"原始數據: {data}")
    print("-" * 50)

client.register_message_callback(detailed_handler)
print("已註冊詳細的訊息處理器")

# 6. 等待一段時間觀察是否有數據
import asyncio

print("等待 30 秒觀察是否有數據...")
await asyncio.sleep(30)
print("觀察完成")