# py-taifex
A Python client for the Taifex Virtual Trading Platform.

## Installation
```bash
pip install py-taifex
```

## Usage
```python
from taifex import Client

async with <PERSON><PERSON>("account", "password") as client:
    await client.login()
    games = await client.get_game_list()
    if games:
        await client.enter_game(games[0].game_id)
        await client.connect_websocket()
        await client.subscribe(["TXFF5"])
```

## Development
Use [uv](https://github.com/astral-sh/uv) to manage dependencies and virtual environment.

```bash
# Clone the repository
git clone https://github.com/coke5151/py-taifex.git
cd py-taifex

# Create a virtual environment and install dependencies
uv sync
```
