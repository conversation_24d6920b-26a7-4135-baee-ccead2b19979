import asyncio
import logging
import os

import dotenv

from src.taifex import Client


async def test_handler(data):
    print("===========")
    print("TEST_HANDLER:")
    print(data)
    print("===========")


async def main() -> None:
    dotenv.load_dotenv()

    account = str(os.getenv("ACCOUNT"))
    password = str(os.getenv("PASSWORD"))

    async with Client(account, password, log_level=logging.INFO) as client:
        await client.login()
        games = await client.get_game_list()
        await client.connect_websocket()
        await client.enter_game(games[0].game_id)
        await client.subscribe(["0000.TW", "TXFF5", "TXFF5.TW-A"])
        client.register_message_callback(test_handler)

        await asyncio.sleep(30)


if __name__ == "__main__":
    asyncio.run(main())
