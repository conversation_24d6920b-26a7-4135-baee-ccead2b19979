from pydantic import BaseModel


class GameInfo(BaseModel):
    date_begin: str
    date_end: str
    date_duration: str
    game_id: str
    game_name: str
    market_mode: str
    market_name: str

    @classmethod
    def from_dict(cls, data: dict):
        return cls(
            date_begin=data["DTBegan"],
            date_end=data["DTEnded"],
            date_duration=data["Duration"],
            game_id=data["GameID"],
            game_name=data["GameName"],
            market_mode=data["MrktMode"],
            market_name=data["MrktName"],
        )
